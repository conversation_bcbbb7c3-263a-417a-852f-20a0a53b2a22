"use client"

import { useState } from "react"
import { CalendarIcon, Filter } from "lucide-react"
import { format } from "date-fns"
import { es } from "date-fns/locale"

import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Slider } from "@/components/ui/slider"

export function CourtFilters() {
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [priceRange, setPriceRange] = useState([10000, 25000])

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">Filtros</h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Fecha</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left font-normal">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "PPP", { locale: es }) : <span>Seleccionar fecha</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar mode="single" selected={date} onSelect={setDate} initialFocus locale={es} />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label>Tipo de Cancha</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox id="indoor" defaultChecked />
                <label
                  htmlFor="indoor"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Indoor
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="outdoor" defaultChecked />
                <label
                  htmlFor="outdoor"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Outdoor
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="premium" defaultChecked />
                <label
                  htmlFor="premium"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Premium
                </label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <Label>Rango de Precio</Label>
              <span className="text-sm text-muted-foreground">
                ${priceRange[0].toLocaleString()} - ${priceRange[1].toLocaleString()}
              </span>
            </div>
            <Slider
              defaultValue={[10000, 25000]}
              max={30000}
              min={5000}
              step={1000}
              onValueChange={setPriceRange}
              className="py-4"
            />
          </div>

          <div className="space-y-2">
            <Label>Horario</Label>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox id="morning" defaultChecked />
                <label
                  htmlFor="morning"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Mañana
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="afternoon" defaultChecked />
                <label
                  htmlFor="afternoon"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Tarde
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="evening" defaultChecked />
                <label
                  htmlFor="evening"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Noche
                </label>
              </div>
            </div>
          </div>
        </div>

        <Button className="w-full mt-6">
          <Filter className="mr-2 h-4 w-4" />
          Aplicar Filtros
        </Button>
      </div>
    </div>
  )
}
