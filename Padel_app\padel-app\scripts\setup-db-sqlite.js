const Database = require('better-sqlite3');
const bcrypt = require('bcryptjs');
const path = require('path');
const fs = require('fs');

async function setupSQLiteDatabase() {
  try {
    console.log('🔄 Configurando base de datos SQLite...');

    // Crear directorio data si no existe
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log('✅ Directorio "data" creado');
    }

    // Crear la base de datos SQLite
    const dbPath = path.join(dataDir, 'padel_app.db');
    const db = new Database(dbPath);

    // Configurar WAL mode para mejor concurrencia
    db.pragma('journal_mode = WAL');
    console.log('✅ Base de datos SQLite creada en:', dbPath);

    // Crear tabla de usuarios
    const createUsersTable = `
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        first_name TEXT NOT NULL,
        last_name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        phone TEXT,
        password_hash TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        is_admin INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    db.exec(createUsersTable);
    console.log('✅ Tabla "users" creada');

    // Crear índices
    db.exec('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)');
    db.exec('CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)');
    console.log('✅ Índices creados');

    // Crear trigger para updated_at
    const createTrigger = `
      CREATE TRIGGER IF NOT EXISTS update_users_updated_at 
      AFTER UPDATE ON users
      BEGIN
        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `;

    db.exec(createTrigger);
    console.log('✅ Trigger para updated_at creado');

    // Verificar si ya existen usuarios
    const existingUsers = db.prepare('SELECT COUNT(*) as count FROM users').get();
    
    if (existingUsers.count === 0) {
      // Crear usuarios de prueba
      const adminPassword = await bcrypt.hash('admin123', 12);
      const userPassword = await bcrypt.hash('user123', 12);

      const insertUser = db.prepare(`
        INSERT INTO users (first_name, last_name, email, phone, password_hash, is_admin) 
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      insertUser.run('Admin', 'Sistema', '<EMAIL>', '+56912345678', adminPassword, 1);
      insertUser.run('Juan', 'Pérez', '<EMAIL>', '+56987654321', userPassword, 0);

      console.log('✅ Usuarios de prueba creados:');
      console.log('   👤 Admin: <EMAIL> / admin123');
      console.log('   👤 Usuario: <EMAIL> / user123');
    } else {
      console.log('ℹ️  Usuarios ya existen en la base de datos');
    }

    // Mostrar usuarios existentes
    const users = db.prepare(`
      SELECT id, first_name, last_name, email, is_admin, created_at 
      FROM users 
      ORDER BY created_at
    `).all();

    console.log('\n📋 Usuarios en la base de datos:');
    users.forEach(user => {
      console.log(`   ${user.is_admin ? '👑' : '👤'} ${user.first_name} ${user.last_name} (${user.email})`);
    });

    // Cerrar la base de datos
    db.close();

    console.log('\n🎉 ¡Base de datos SQLite configurada exitosamente!');
    console.log('\n📝 Próximos pasos:');
    console.log('   1. Ejecuta: npm run dev');
    console.log('   2. Ve a http://localhost:3000');
    console.log('   3. Prueba el registro y login en la aplicación');
    console.log('   4. La base de datos está en: ' + dbPath);

  } catch (error) {
    console.error('❌ Error configurando la base de datos SQLite:', error);
    process.exit(1);
  }
}

// Ejecutar el script
setupSQLiteDatabase();
