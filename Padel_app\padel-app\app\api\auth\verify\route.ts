import { NextRequest, NextResponse } from 'next/server';
import { getUserById } from '@/lib/database-sqlite';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth';
import type { AuthResponse, ApiError } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Token no proporcionado',
        errors: ['Se requiere autenticación']
      }, { status: 401 });
    }

    // Verificar token
    const decoded = verifyToken(token);
    
    // Buscar usuario en la base de datos
    const user = getUserById(decoded.userId);
    if (!user) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Usuario no encontrado',
        errors: ['El usuario asociado al token no existe']
      }, { status: 404 });
    }

    // Verificar si el usuario está activo
    if (!user.is_active) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Cuenta desactivada',
        errors: ['Tu cuenta ha sido desactivada']
      }, { status: 403 });
    }

    // Respuesta exitosa
    const response: AuthResponse = {
      success: true,
      message: 'Token válido',
      user: {
        id: user.id,
        firstName: user.first_name,
        lastName: user.last_name,
        email: user.email,
        isAdmin: Boolean(user.is_admin)
      }
    };

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('Error en verificación de token:', error);

    if (error instanceof Error && error.message === 'Token inválido') {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Token inválido',
        errors: ['El token ha expirado o es inválido']
      }, { status: 401 });
    }

    return NextResponse.json<ApiError>({
      success: false,
      message: 'Error interno del servidor',
      errors: ['Ocurrió un error inesperado']
    }, { status: 500 });
  }
}
