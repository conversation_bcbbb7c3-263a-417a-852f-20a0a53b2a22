"use client"

import { useState } from "react"
import { CalendarIcon, MoreHorizontal, Search } from "lucide-react"
import { format } from "date-fns"
import { es } from "date-fns/locale"

import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

// Datos de ejemplo para las reservas
const reservations = [
  {
    id: "RES-1234",
    user: "<PERSON>",
    email: "<EMAIL>",
    court: "Cancha 2",
    date: new Date(2024, 5, 15, 18, 0),
    duration: "1 hora",
    status: "confirmed",
    payment: "completed",
    amount: 15000,
  },
  {
    id: "RES-1235",
    user: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    court: "Cancha 1",
    date: new Date(2024, 5, 15, 19, 0),
    duration: "1 hora",
    status: "confirmed",
    payment: "completed",
    amount: 15000,
  },
  {
    id: "RES-1236",
    user: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    court: "Cancha 3",
    date: new Date(2024, 5, 16, 10, 0),
    duration: "1 hora",
    status: "confirmed",
    payment: "pending",
    amount: 20000,
  },
  {
    id: "RES-1237",
    user: "Ana Martínez",
    email: "<EMAIL>",
    court: "Cancha 4",
    date: new Date(2024, 5, 16, 17, 0),
    duration: "1 hora",
    status: "confirmed",
    payment: "completed",
    amount: 15000,
  },
  {
    id: "RES-1238",
    user: "Pedro Sánchez",
    email: "<EMAIL>",
    court: "Cancha 1",
    date: new Date(2024, 5, 17, 11, 0),
    duration: "1 hora",
    status: "pending",
    payment: "pending",
    amount: 15000,
  },
  {
    id: "RES-1239",
    user: "Laura Torres",
    email: "<EMAIL>",
    court: "Cancha 2",
    date: new Date(2024, 5, 17, 16, 0),
    duration: "1 hora",
    status: "cancelled",
    payment: "refunded",
    amount: 15000,
  },
]

export function AdminReservations() {
  const [date, setDate] = useState<Date | undefined>(new Date())

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="flex w-full items-center space-x-2 sm:w-auto">
          <Input
            placeholder="Buscar reservas..."
            className="w-full sm:w-[300px]"
            prefix={<Search className="h-4 w-4 text-muted-foreground" />}
          />
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, "PPP", { locale: es }) : <span>Seleccionar fecha</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar mode="single" selected={date} onSelect={setDate} initialFocus locale={es} />
            </PopoverContent>
          </Popover>
        </div>
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <Select defaultValue="all">
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Estado" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos los estados</SelectItem>
              <SelectItem value="confirmed">Confirmadas</SelectItem>
              <SelectItem value="pending">Pendientes</SelectItem>
              <SelectItem value="cancelled">Canceladas</SelectItem>
            </SelectContent>
          </Select>
          <Button>Exportar</Button>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Usuario</TableHead>
              <TableHead>Cancha</TableHead>
              <TableHead>Fecha y Hora</TableHead>
              <TableHead>Estado</TableHead>
              <TableHead>Pago</TableHead>
              <TableHead className="text-right">Monto</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {reservations.map((reservation) => (
              <TableRow key={reservation.id}>
                <TableCell className="font-medium">{reservation.id}</TableCell>
                <TableCell>
                  <div>
                    <div>{reservation.user}</div>
                    <div className="text-sm text-muted-foreground">{reservation.email}</div>
                  </div>
                </TableCell>
                <TableCell>{reservation.court}</TableCell>
                <TableCell>{format(reservation.date, "dd/MM/yyyy HH:mm")}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      reservation.status === "confirmed"
                        ? "default"
                        : reservation.status === "pending"
                          ? "outline"
                          : "destructive"
                    }
                  >
                    {reservation.status === "confirmed"
                      ? "Confirmada"
                      : reservation.status === "pending"
                        ? "Pendiente"
                        : "Cancelada"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={
                      reservation.payment === "completed"
                        ? "default"
                        : reservation.payment === "pending"
                          ? "outline"
                          : "destructive"
                    }
                    className={
                      reservation.payment === "completed" ? "bg-green-500" : reservation.payment === "pending" ? "" : ""
                    }
                  >
                    {reservation.payment === "completed"
                      ? "Pagado"
                      : reservation.payment === "pending"
                        ? "Pendiente"
                        : "Reembolsado"}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">${reservation.amount.toLocaleString()}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Abrir menú</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>Ver detalles</DropdownMenuItem>
                      <DropdownMenuItem>Editar reserva</DropdownMenuItem>
                      <DropdownMenuItem>Cancelar reserva</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
