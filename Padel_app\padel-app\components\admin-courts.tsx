"use client"

import { useState } from "react"
import { MoreH<PERSON>zon<PERSON>, Plus } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"

// Datos de ejemplo para las canchas
const courts = [
  {
    id: 1,
    name: "Cancha 1",
    type: "Indoor",
    status: "active",
    basePrice: 15000,
    peakPrice: 18000,
    maintenance: false,
  },
  {
    id: 2,
    name: "Cancha 2",
    type: "Outdoor",
    status: "active",
    basePrice: 12000,
    peakPrice: 15000,
    maintenance: false,
  },
  {
    id: 3,
    name: "Cancha 3",
    type: "Indoor Premium",
    status: "active",
    basePrice: 20000,
    peakPrice: 24000,
    maintenance: false,
  },
  {
    id: 4,
    name: "Cancha 4",
    type: "Indoor",
    status: "active",
    basePrice: 15000,
    peakPrice: 18000,
    maintenance: true,
  },
]

export function AdminCourts() {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <div className="space-y-4">
      <div className="flex justify-between">
        <h2 className="text-xl font-semibold">Gestión de Canchas</h2>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Agregar Cancha
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Agregar Nueva Cancha</DialogTitle>
              <DialogDescription>Completa la información para agregar una nueva cancha al sistema.</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Nombre
                </Label>
                <Input id="name" placeholder="Cancha 5" className="col-span-3" />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="type" className="text-right">
                  Tipo
                </Label>
                <Select>
                  <SelectTrigger id="type" className="col-span-3">
                    <SelectValue placeholder="Seleccionar tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="indoor">Indoor</SelectItem>
                    <SelectItem value="outdoor">Outdoor</SelectItem>
                    <SelectItem value="premium">Indoor Premium</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="basePrice" className="text-right">
                  Precio Base
                </Label>
                <Input id="basePrice" type="number" placeholder="15000" className="col-span-3" />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="peakPrice" className="text-right">
                  Precio Hora Pico
                </Label>
                <Input id="peakPrice" type="number" placeholder="18000" className="col-span-3" />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancelar
              </Button>
              <Button onClick={() => setIsDialogOpen(false)}>Guardar</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {courts.map((court) => (
          <Card key={court.id} className={court.maintenance ? "border-yellow-300" : ""}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle>{court.name}</CardTitle>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Abrir menú</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>Editar</DropdownMenuItem>
                    <DropdownMenuItem>Ver reservas</DropdownMenuItem>
                    <DropdownMenuItem>Programar mantenimiento</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <CardDescription>
                <Badge variant="outline">{court.type}</Badge>
                {court.maintenance && (
                  <Badge variant="outline" className="ml-2 bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
                    En mantenimiento
                  </Badge>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm">
                  <p className="text-muted-foreground">Precio Base</p>
                  <p className="font-medium">${court.basePrice.toLocaleString()}</p>
                </div>
                <div className="text-sm">
                  <p className="text-muted-foreground">Precio Hora Pico</p>
                  <p className="font-medium">${court.peakPrice.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t pt-4 flex justify-between">
              <div className="flex items-center space-x-2">
                <Switch id={`active-${court.id}`} defaultChecked={court.status === "active"} />
                <Label htmlFor={`active-${court.id}`}>Activa</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id={`maintenance-${court.id}`} defaultChecked={court.maintenance} />
                <Label htmlFor={`maintenance-${court.id}`}>Mantenimiento</Label>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
