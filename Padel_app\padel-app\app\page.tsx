import Link from "next/link"
import { CalendarD<PERSON>, Clock, CreditCard, Users } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { HeroSection } from "@/components/hero-section"
import { FeatureCard } from "@/components/feature-card"
import { AvailableCourts } from "@/components/available-courts"

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          <Link href="/" className="flex items-center gap-2 font-bold text-xl text-green-600">
            <span>UCENIN</span>
            <span className="text-sm font-normal text-muted-foreground">Pádel</span>
          </Link>
          <nav className="ml-auto flex gap-4 sm:gap-6">
            <Link href="/reservas" className="text-sm font-medium hover:underline underline-offset-4">
              Reservas
            </Link>
            <Link href="/canchas" className="text-sm font-medium hover:underline underline-offset-4">
              Canchas
            </Link>
            <Link href="/precios" className="text-sm font-medium hover:underline underline-offset-4">
              Precios
            </Link>
            <Link href="/contacto" className="text-sm font-medium hover:underline underline-offset-4">
              Contacto
            </Link>
          </nav>
          <div className="ml-4 flex items-center gap-2">
            <Link href="/login">
              <Button variant="outline" size="sm">
                Iniciar Sesión
              </Button>
            </Link>
            <Link href="/registro">
              <Button size="sm">Registrarse</Button>
            </Link>
          </div>
        </div>
      </header>
      <main className="flex-1">
        <HeroSection />

        <section className="container py-12 md:py-16">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <FeatureCard
              icon={<CalendarDays className="h-6 w-6 text-green-600" />}
              title="Reserva Online"
              description="Reserva tu cancha en cualquier momento desde cualquier dispositivo."
            />
            <FeatureCard
              icon={<Clock className="h-6 w-6 text-green-600" />}
              title="Disponibilidad en Tiempo Real"
              description="Visualiza la disponibilidad de canchas actualizada al instante."
            />
            <FeatureCard
              icon={<CreditCard className="h-6 w-6 text-green-600" />}
              title="Pagos Digitales"
              description="Paga de forma segura con múltiples métodos de pago digital."
            />
            <FeatureCard
              icon={<Users className="h-6 w-6 text-green-600" />}
              title="Notificaciones Automáticas"
              description="Recibe confirmaciones y recordatorios de tus reservas."
            />
          </div>
        </section>

        <section className="bg-muted py-12 md:py-16">
          <div className="container">
            <h2 className="text-2xl font-bold tracking-tight mb-6">Canchas Disponibles Hoy</h2>
            <AvailableCourts />
            <div className="mt-8 text-center">
              <Link href="/reservas">
                <Button size="lg">Ver Todas las Canchas</Button>
              </Link>
            </div>
          </div>
        </section>
      </main>
      <footer className="border-t py-6 md:py-8">
        <div className="container flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex flex-col gap-1">
            <Link href="/" className="font-bold text-green-600">
              UCENIN Pádel
            </Link>
            <p className="text-sm text-muted-foreground">© 2024 UCENIN. Todos los derechos reservados.</p>
          </div>
          <nav className="flex gap-4 sm:gap-6">
            <Link href="/terminos" className="text-sm font-medium text-muted-foreground hover:underline">
              Términos
            </Link>
            <Link href="/privacidad" className="text-sm font-medium text-muted-foreground hover:underline">
              Privacidad
            </Link>
            <Link href="/contacto" className="text-sm font-medium text-muted-foreground hover:underline">
              Contacto
            </Link>
          </nav>
        </div>
      </footer>
    </div>
  )
}
