"use client"

import { cn } from "@/lib/utils"
import React from "react"
import { useState } from "react"
import Link from "next/link"
import { ChevronLeft, ChevronRight } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

// Datos de ejemplo para las canchas y horarios
const courts = [
  { id: 1, name: "Cancha 1", type: "Indoor" },
  { id: 2, name: "Cancha 2", type: "Outdoor" },
  { id: 3, name: "Cancha 3", type: "Indoor Premium" },
  { id: 4, name: "Cancha 4", type: "Indoor" },
]

const timeSlots = [
  "09:00",
  "10:00",
  "11:00",
  "12:00",
  "13:00",
  "14:00",
  "15:00",
  "16:00",
  "17:00",
  "18:00",
  "19:00",
  "20:00",
  "21:00",
]

// Datos de ejemplo para disponibilidad (1: disponible, 2: reservado, 3: mantenimiento)
const availability = {
  1: {
    "09:00": 1,
    "10:00": 1,
    "11:00": 1,
    "12:00": 2,
    "13:00": 2,
    "14:00": 1,
    "15:00": 1,
    "16:00": 1,
    "17:00": 1,
    "18:00": 2,
    "19:00": 2,
    "20:00": 1,
    "21:00": 1,
  },
  2: {
    "09:00": 1,
    "10:00": 2,
    "11:00": 2,
    "12:00": 1,
    "13:00": 1,
    "14:00": 3,
    "15:00": 3,
    "16:00": 1,
    "17:00": 1,
    "18:00": 1,
    "19:00": 1,
    "20:00": 2,
    "21:00": 2,
  },
  3: {
    "09:00": 3,
    "10:00": 3,
    "11:00": 1,
    "12:00": 1,
    "13:00": 1,
    "14:00": 1,
    "15:00": 2,
    "16:00": 2,
    "17:00": 1,
    "18:00": 1,
    "19:00": 1,
    "20:00": 1,
    "21:00": 1,
  },
  4: {
    "09:00": 1,
    "10:00": 1,
    "11:00": 1,
    "12:00": 1,
    "13:00": 2,
    "14:00": 2,
    "15:00": 1,
    "16:00": 1,
    "17:00": 3,
    "18:00": 3,
    "19:00": 1,
    "20:00": 1,
    "21:00": 2,
  },
}

// Precios por hora según el tipo de cancha y horario
const getPriceForSlot = (courtId: number, time: string) => {
  const court = courts.find((c) => c.id === courtId)
  const hour = Number.parseInt(time.split(":")[0])

  // Precios base según tipo de cancha
  let basePrice = court?.type === "Indoor Premium" ? 20000 : court?.type === "Indoor" ? 15000 : 12000

  // Ajuste por horario (más caro en horas pico)
  if (hour >= 18 && hour <= 21) {
    basePrice = basePrice * 1.2 // 20% más caro en horario pico
  } else if (hour >= 12 && hour <= 14) {
    basePrice = basePrice * 0.9 // 10% más barato en horario de almuerzo
  }

  return Math.round(basePrice)
}

export function CourtCalendar() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedSlot, setSelectedSlot] = useState<{ courtId: number; time: string } | null>(null)

  // Formatear la fecha actual para mostrar
  const formattedDate = currentDate.toLocaleDateString("es-ES", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  })

  // Función para cambiar de día
  const changeDate = (days: number) => {
    const newDate = new Date(currentDate)
    newDate.setDate(newDate.getDate() + days)
    setCurrentDate(newDate)
  }

  // Función para manejar la selección de un slot
  const handleSlotSelect = (courtId: number, time: string) => {
    if (availability[courtId][time] === 1) {
      // Solo si está disponible
      setSelectedSlot({ courtId, time })
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold capitalize">{formattedDate}</h2>
        <div className="flex space-x-2">
          <Button variant="outline" size="icon" onClick={() => changeDate(-1)}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={() => changeDate(1)}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <div className="min-w-[800px]">
          <div className="grid grid-cols-[150px_repeat(13,minmax(80px,1fr))] border rounded-lg">
            {/* Header con horarios */}
            <div className="bg-muted p-2 font-medium border-r">Canchas</div>
            {timeSlots.map((time) => (
              <div key={time} className="bg-muted p-2 text-center font-medium border-r last:border-r-0">
                {time}
              </div>
            ))}

            {/* Filas de canchas */}
            {courts.map((court) => (
              <React.Fragment key={court.id}>
                <div className="p-2 border-t border-r font-medium flex items-center">
                  <div>
                    <div>{court.name}</div>
                    <Badge variant="outline" className="mt-1">
                      {court.type}
                    </Badge>
                  </div>
                </div>
                {timeSlots.map((time) => {
                  const status = availability[court.id][time]
                  const isSelected = selectedSlot?.courtId === court.id && selectedSlot?.time === time

                  return (
                    <div
                      key={`${court.id}-${time}`}
                      className={cn(
                        "p-2 border-t border-r last:border-r-0 text-center cursor-pointer transition-colors",
                        status === 1 ? "hover:bg-green-50" : "",
                        status === 2 ? "bg-red-50 cursor-not-allowed" : "",
                        status === 3 ? "bg-gray-100 cursor-not-allowed" : "",
                        isSelected ? "bg-green-100 ring-2 ring-green-500" : "",
                      )}
                      onClick={() => status === 1 && handleSlotSelect(court.id, time)}
                    >
                      {status === 1 && (
                        <div className="flex flex-col items-center">
                          <span className="text-green-600 font-medium">Disponible</span>
                          <span className="text-sm">${getPriceForSlot(court.id, time).toLocaleString()}</span>
                        </div>
                      )}
                      {status === 2 && <span className="text-red-600">Reservado</span>}
                      {status === 3 && <span className="text-gray-500">Mantenimiento</span>}
                    </div>
                  )
                })}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>

      {selectedSlot && (
        <Card className="mt-6 border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle>Reserva Seleccionada</CardTitle>
            <CardDescription>Revisa los detalles de tu selección antes de continuar</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Cancha</p>
                  <p>{courts.find((c) => c.id === selectedSlot.courtId)?.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Tipo</p>
                  <p>{courts.find((c) => c.id === selectedSlot.courtId)?.type}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Fecha</p>
                  <p>{currentDate.toLocaleDateString("es-ES")}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Hora</p>
                  <p>
                    {selectedSlot.time} - {Number.parseInt(selectedSlot.time.split(":")[0]) + 1}:00
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Precio</p>
                  <p className="font-bold">
                    ${getPriceForSlot(selectedSlot.courtId, selectedSlot.time).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setSelectedSlot(null)}>
              Cancelar
            </Button>
            <Link
              href={`/reservas/confirmar?court=${selectedSlot.courtId}&date=${currentDate.toISOString()}&time=${selectedSlot.time}`}
            >
              <Button className="bg-green-600 hover:bg-green-700">Continuar con la Reserva</Button>
            </Link>
          </CardFooter>
        </Card>
      )}
    </div>
  )
}
