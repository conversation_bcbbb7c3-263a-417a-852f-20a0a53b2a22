import Database from 'better-sqlite3';
import path from 'path';

// Crear la base de datos SQLite
const dbPath = path.join(process.cwd(), 'data', 'padel_app.db');
const db = new Database(dbPath);

// Configurar WAL mode para mejor concurrencia
db.pragma('journal_mode = WAL');

// Función para inicializar la base de datos
export function initializeDatabase() {
  // Crear directorio data si no existe
  const fs = require('fs');
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }

  // Crear tabla de usuarios
  const createUsersTable = `
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
      first_name TEXT NOT NULL,
      last_name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      phone TEXT,
      password_hash TEXT NOT NULL,
      is_active INTEGER DEFAULT 1,
      is_admin INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `;

  db.exec(createUsersTable);

  // Crear índices
  db.exec('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)');
  db.exec('CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)');

  // Crear trigger para updated_at
  const createTrigger = `
    CREATE TRIGGER IF NOT EXISTS update_users_updated_at 
    AFTER UPDATE ON users
    BEGIN
      UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END
  `;

  db.exec(createTrigger);

  console.log('✅ Base de datos SQLite inicializada');
}

// Tipos para el usuario
export interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  password_hash: string;
  is_active: number;
  is_admin: number;
  created_at: string;
  updated_at: string;
}

export interface CreateUserData {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  password_hash: string;
}

// Funciones para manejo de usuarios
export function createUser(userData: CreateUserData): User {
  const { first_name, last_name, email, phone, password_hash } = userData;
  
  const stmt = db.prepare(`
    INSERT INTO users (first_name, last_name, email, phone, password_hash) 
    VALUES (?, ?, ?, ?, ?)
  `);
  
  const result = stmt.run(first_name, last_name, email, phone, password_hash);
  
  // Obtener el usuario creado
  const getUser = db.prepare('SELECT * FROM users WHERE rowid = ?');
  return getUser.get(result.lastInsertRowid) as User;
}

export function getUserByEmail(email: string): User | null {
  const stmt = db.prepare('SELECT * FROM users WHERE email = ? AND is_active = 1');
  return stmt.get(email) as User | null;
}

export function getUserById(id: string): User | null {
  const stmt = db.prepare('SELECT * FROM users WHERE id = ? AND is_active = 1');
  return stmt.get(id) as User | null;
}

export function updateUserLastLogin(id: string): void {
  const stmt = db.prepare('UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?');
  stmt.run(id);
}

// Función para cerrar la base de datos (útil para testing)
export function closeDatabase(): void {
  db.close();
}

// Función para verificar la conexión a la base de datos
export function testConnection(): boolean {
  try {
    const result = db.prepare('SELECT datetime("now") as now').get();
    console.log('Database connection successful:', result);
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
}

// Inicializar la base de datos al importar el módulo
initializeDatabase();
