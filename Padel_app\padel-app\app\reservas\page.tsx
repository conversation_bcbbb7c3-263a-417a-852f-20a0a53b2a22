import { Suspense } from "react"
import Link from "next/link"
import { ChevronLeft } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { CourtCalendar } from "@/components/court-calendar"
import { CourtFilters } from "@/components/court-filters"

export default function ReservasPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          <Link href="/" className="flex items-center gap-2 font-bold text-xl text-green-600">
            <span>UCENIN</span>
            <span className="text-sm font-normal text-muted-foreground">Pádel</span>
          </Link>
          <nav className="ml-auto flex gap-4 sm:gap-6">
            <Link href="/reservas" className="text-sm font-medium text-green-600 underline underline-offset-4">
              Reservas
            </Link>
            <Link href="/canchas" className="text-sm font-medium hover:underline underline-offset-4">
              Canchas
            </Link>
            <Link href="/precios" className="text-sm font-medium hover:underline underline-offset-4">
              Precios
            </Link>
            <Link href="/contacto" className="text-sm font-medium hover:underline underline-offset-4">
              Contacto
            </Link>
          </nav>
          <div className="ml-4 flex items-center gap-2">
            <Link href="/login">
              <Button variant="outline" size="sm">
                Iniciar Sesión
              </Button>
            </Link>
            <Link href="/registro">
              <Button size="sm">Registrarse</Button>
            </Link>
          </div>
        </div>
      </header>
      <main className="flex-1 container py-6 md:py-10">
        <div className="flex items-center mb-6">
          <Link href="/" className="flex items-center text-sm text-muted-foreground hover:text-foreground mr-4">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Volver al inicio
          </Link>
          <h1 className="text-2xl font-bold tracking-tight">Reserva tu Cancha</h1>
        </div>

        <div className="grid gap-6 md:grid-cols-[300px_1fr]">
          <aside className="md:border-r pr-6">
            <CourtFilters />
          </aside>
          <div className="space-y-6">
            <Suspense fallback={<div>Cargando calendario...</div>}>
              <CourtCalendar />
            </Suspense>
          </div>
        </div>
      </main>
      <footer className="border-t py-6 md:py-8">
        <div className="container flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex flex-col gap-1">
            <Link href="/" className="font-bold text-green-600">
              UCENIN Pádel
            </Link>
            <p className="text-sm text-muted-foreground">© 2024 UCENIN. Todos los derechos reservados.</p>
          </div>
          <nav className="flex gap-4 sm:gap-6">
            <Link href="/terminos" className="text-sm font-medium text-muted-foreground hover:underline">
              Términos
            </Link>
            <Link href="/privacidad" className="text-sm font-medium text-muted-foreground hover:underline">
              Privacidad
            </Link>
            <Link href="/contacto" className="text-sm font-medium text-muted-foreground hover:underline">
              Contacto
            </Link>
          </nav>
        </div>
      </footer>
    </div>
  )
}
