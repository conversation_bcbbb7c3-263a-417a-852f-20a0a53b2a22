import Link from "next/link"
import { Button } from "@/components/ui/button"

export function HeroSection() {
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-b from-green-50 to-white dark:from-green-950 dark:to-background">
      <div className="container px-4 md:px-6">
        <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
          <div className="flex flex-col justify-center space-y-4">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl">
                Reserva tu cancha de pádel en segundos
              </h1>
              <p className="max-w-[600px] text-muted-foreground md:text-xl">
                Gestiona tus reservas de forma fácil y rápida. Visualiza la disponibilidad en tiempo real y paga de
                forma segura.
              </p>
            </div>
            <div className="flex flex-col gap-2 min-[400px]:flex-row">
              <Link href="/reservas">
                <Button size="lg" className="bg-green-600 hover:bg-green-700">
                  Reservar Ahora
                </Button>
              </Link>
              <Link href="/canchas">
                <Button size="lg" variant="outline">
                  Ver Canchas
                </Button>
              </Link>
            </div>
          </div>
          <div className="flex items-center justify-center">
            <div className="relative h-[300px] w-full overflow-hidden rounded-xl md:h-[400px] lg:h-[500px]">
              <img
                alt="Canchas de pádel UCENIN"
                className="object-cover w-full h-full"
                src="/placeholder.svg?height=500&width=800"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
