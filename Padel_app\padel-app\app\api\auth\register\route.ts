import { NextRequest, NextResponse } from 'next/server';
import { createUser, getUserByEmail } from '@/lib/database-sqlite';
import { hashPassword, validateEmail, validatePassword, validatePhone, generateToken } from '@/lib/auth';
import type { AuthResponse, ApiError } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { firstName, lastName, email, phone, password, confirmPassword } = body;

    // Validaciones básicas
    if (!firstName || !lastName || !email || !password) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Todos los campos obligatorios deben ser completados',
        errors: ['Nombre, apellido, email y contraseña son obligatorios']
      }, { status: 400 });
    }

    // Validar email
    if (!validateEmail(email)) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Email inválido',
        errors: ['Por favor ingresa un email válido']
      }, { status: 400 });
    }

    // Validar contraseña
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Contraseña no cumple con los requisitos',
        errors: passwordValidation.errors
      }, { status: 400 });
    }

    // Verificar que las contraseñas coincidan
    if (password !== confirmPassword) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Las contraseñas no coinciden',
        errors: ['Las contraseñas deben ser idénticas']
      }, { status: 400 });
    }

    // Validar teléfono si se proporciona
    if (phone && !validatePhone(phone)) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Número de teléfono inválido',
        errors: ['Por favor ingresa un número de teléfono chileno válido']
      }, { status: 400 });
    }

    // Verificar si el usuario ya existe
    const existingUser = getUserByEmail(email.toLowerCase());
    if (existingUser) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'El email ya está registrado',
        errors: ['Ya existe una cuenta con este email']
      }, { status: 409 });
    }

    // Hashear la contraseña
    const hashedPassword = await hashPassword(password);

    // Crear el usuario
    const newUser = createUser({
      first_name: firstName.trim(),
      last_name: lastName.trim(),
      email: email.toLowerCase().trim(),
      phone: phone?.trim() || null,
      password_hash: hashedPassword
    });

    // Generar token JWT
    const token = generateToken({
      ...newUser,
      is_active: Boolean(newUser.is_active),
      is_admin: Boolean(newUser.is_admin),
      created_at: new Date(newUser.created_at),
      updated_at: new Date(newUser.updated_at)
    });

    // Respuesta exitosa
    const response: AuthResponse = {
      success: true,
      message: 'Usuario registrado exitosamente',
      user: {
        id: newUser.id,
        firstName: newUser.first_name,
        lastName: newUser.last_name,
        email: newUser.email,
        isAdmin: Boolean(newUser.is_admin)
      },
      token
    };

    return NextResponse.json(response, { status: 201 });

  } catch (error) {
    console.error('Error en registro:', error);
    
    // Manejar errores específicos de base de datos
    if (error instanceof Error) {
      if (error.message.includes('duplicate key')) {
        return NextResponse.json<ApiError>({
          success: false,
          message: 'El email ya está registrado',
          errors: ['Ya existe una cuenta con este email']
        }, { status: 409 });
      }
    }

    return NextResponse.json<ApiError>({
      success: false,
      message: 'Error interno del servidor',
      errors: ['Ocurrió un error inesperado. Por favor intenta nuevamente.']
    }, { status: 500 });
  }
}
