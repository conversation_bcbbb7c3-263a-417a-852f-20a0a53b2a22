"use client"

import { useState } from "react"
import Link from "next/link"
import { Calendar, Clock } from "lucide-react"

import { <PERSON>ton } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

// Datos de ejemplo para las canchas disponibles
const availableCourts = [
  {
    id: 1,
    name: "Cancha 1",
    type: "Indoor",
    availableSlots: [
      { id: 101, time: "10:00 - 11:00", price: 15000 },
      { id: 102, time: "11:00 - 12:00", price: 15000 },
      { id: 103, time: "17:00 - 18:00", price: 18000 },
    ],
  },
  {
    id: 2,
    name: "Cancha 2",
    type: "Outdoor",
    availableSlots: [
      { id: 201, time: "09:00 - 10:00", price: 12000 },
      { id: 202, time: "16:00 - 17:00", price: 15000 },
      { id: 203, time: "19:00 - 20:00", price: 18000 },
    ],
  },
  {
    id: 3,
    name: "Cancha 3",
    type: "Indoor Premium",
    availableSlots: [
      { id: 301, time: "14:00 - 15:00", price: 20000 },
      { id: 302, time: "18:00 - 19:00", price: 22000 },
    ],
  },
]

export function AvailableCourts() {
  const [selectedCourt, setSelectedCourt] = useState<number | null>(null)

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {availableCourts.map((court) => (
        <Card key={court.id} className={selectedCourt === court.id ? "border-green-500 shadow-lg" : ""}>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle>{court.name}</CardTitle>
              <Badge variant={court.type === "Indoor Premium" ? "default" : "outline"}>{court.type}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center text-sm text-muted-foreground">
                <Calendar className="mr-1 h-4 w-4" />
                <span>Hoy, {new Date().toLocaleDateString("es-ES", { day: "numeric", month: "long" })}</span>
              </div>
              <div className="mt-4 space-y-2">
                <h4 className="text-sm font-medium">Horarios disponibles:</h4>
                <div className="grid grid-cols-2 gap-2">
                  {court.availableSlots.map((slot) => (
                    <div key={slot.id} className="flex items-center justify-between rounded-md border p-2 text-sm">
                      <div className="flex items-center">
                        <Clock className="mr-1 h-4 w-4 text-muted-foreground" />
                        <span>{slot.time}</span>
                      </div>
                      <span className="font-medium">${slot.price.toLocaleString()}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Link href={`/reservas/${court.id}`} className="w-full">
              <Button className="w-full" variant="outline" onClick={() => setSelectedCourt(court.id)}>
                Reservar
              </Button>
            </Link>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
