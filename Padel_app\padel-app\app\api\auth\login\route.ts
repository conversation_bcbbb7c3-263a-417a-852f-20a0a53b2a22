import { NextRequest, NextResponse } from 'next/server';
import { getUserByEmail, updateUserLastLogin } from '@/lib/database-sqlite';
import { verifyPassword, validateEmail, generateToken } from '@/lib/auth';
import type { AuthResponse, ApiError } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validaciones básicas
    if (!email || !password) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Email y contraseña son obligatorios',
        errors: ['Por favor completa todos los campos']
      }, { status: 400 });
    }

    // Validar formato de email
    if (!validateEmail(email)) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Email inválido',
        errors: ['Por favor ingresa un email válido']
      }, { status: 400 });
    }

    // Buscar usuario por email
    const user = getUserByEmail(email.toLowerCase().trim());
    if (!user) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Credenciales inválidas',
        errors: ['Email o contraseña incorrectos']
      }, { status: 401 });
    }

    // Verificar si el usuario está activo
    if (!user.is_active) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Cuenta desactivada',
        errors: ['Tu cuenta ha sido desactivada. Contacta al administrador.']
      }, { status: 403 });
    }

    // Verificar contraseña
    const isPasswordValid = await verifyPassword(password, user.password_hash);
    if (!isPasswordValid) {
      return NextResponse.json<ApiError>({
        success: false,
        message: 'Credenciales inválidas',
        errors: ['Email o contraseña incorrectos']
      }, { status: 401 });
    }

    // Actualizar último login
    updateUserLastLogin(user.id);

    // Generar token JWT
    const token = generateToken({
      ...user,
      is_active: Boolean(user.is_active),
      is_admin: Boolean(user.is_admin),
      created_at: new Date(user.created_at),
      updated_at: new Date(user.updated_at)
    });

    // Respuesta exitosa
    const response: AuthResponse = {
      success: true,
      message: 'Inicio de sesión exitoso',
      user: {
        id: user.id,
        firstName: user.first_name,
        lastName: user.last_name,
        email: user.email,
        isAdmin: Boolean(user.is_admin)
      },
      token
    };

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('Error en login:', error);

    return NextResponse.json<ApiError>({
      success: false,
      message: 'Error interno del servidor',
      errors: ['Ocurrió un error inesperado. Por favor intenta nuevamente.']
    }, { status: 500 });
  }
}
