"use client"

import { useState } from "react"
import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { ChevronLeft, CreditCard, CalendarDays, Clock, Users, CheckCircle } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function ConfirmarReservaPage() {
  const searchParams = useSearchParams()
  const courtId = searchParams.get("court")
  const date = searchParams.get("date") ? new Date(searchParams.get("date") as string) : new Date()
  const time = searchParams.get("time")

  const [isConfirmed, setIsConfirmed] = useState(false)

  // Datos de ejemplo para la cancha seleccionada
  const courtData = {
    id: courtId,
    name: `Cancha ${courtId}`,
    type: courtId === "3" ? "Indoor Premium" : courtId === "2" ? "Outdoor" : "Indoor",
    price: courtId === "3" ? 20000 : courtId === "2" ? 12000 : 15000,
  }

  // Función para manejar la confirmación de la reserva
  const handleConfirmReservation = () => {
    // Aquí iría la lógica para procesar el pago y confirmar la reserva
    setIsConfirmed(true)
  }

  // Formatear la fecha para mostrar
  const formattedDate = date.toLocaleDateString("es-ES", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  })

  if (isConfirmed) {
    return (
      <div className="flex flex-col min-h-screen">
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container flex h-16 items-center">
            <Link href="/" className="flex items-center gap-2 font-bold text-xl text-green-600">
              <span>UCENIN</span>
              <span className="text-sm font-normal text-muted-foreground">Pádel</span>
            </Link>
          </div>
        </header>
        <main className="flex-1 container py-10 flex items-center justify-center">
          <Card className="max-w-md w-full">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 flex items-center justify-center rounded-full bg-green-100 mb-4">
                <CheckCircle className="h-10 w-10 text-green-600" />
              </div>
              <CardTitle className="text-2xl">¡Reserva Confirmada!</CardTitle>
              <CardDescription>Tu reserva ha sido procesada correctamente</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="rounded-lg border p-4">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Cancha:</span>
                      <span className="font-medium">{courtData.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Fecha:</span>
                      <span className="font-medium">{formattedDate}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Hora:</span>
                      <span className="font-medium">
                        {time} - {Number.parseInt(time?.split(":")[0] || "0") + 1}:00
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Código de Reserva:</span>
                      <span className="font-medium">UC-{Math.floor(Math.random() * 10000)}</span>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-center text-muted-foreground">
                  Hemos enviado un correo electrónico con los detalles de tu reserva
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-2">
              <Link href="/reservas" className="w-full">
                <Button className="w-full">Ver Mis Reservas</Button>
              </Link>
              <Link href="/" className="w-full">
                <Button variant="outline" className="w-full">
                  Volver al Inicio
                </Button>
              </Link>
            </CardFooter>
          </Card>
        </main>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          <Link href="/" className="flex items-center gap-2 font-bold text-xl text-green-600">
            <span>UCENIN</span>
            <span className="text-sm font-normal text-muted-foreground">Pádel</span>
          </Link>
        </div>
      </header>
      <main className="flex-1 container py-6 md:py-10">
        <div className="flex items-center mb-6">
          <Link href="/reservas" className="flex items-center text-sm text-muted-foreground hover:text-foreground mr-4">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Volver a Reservas
          </Link>
          <h1 className="text-2xl font-bold tracking-tight">Confirmar Reserva</h1>
        </div>

        <div className="grid gap-6 md:grid-cols-[1fr_350px]">
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Detalles de la Reserva</CardTitle>
                <CardDescription>Revisa los detalles de tu reserva</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <Label className="text-muted-foreground">Cancha</Label>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-2 text-green-600" />
                      <span>
                        {courtData.name} ({courtData.type})
                      </span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-muted-foreground">Fecha</Label>
                    <div className="flex items-center">
                      <CalendarDays className="h-4 w-4 mr-2 text-green-600" />
                      <span>{formattedDate}</span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-muted-foreground">Hora</Label>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-green-600" />
                      <span>
                        {time} - {Number.parseInt(time?.split(":")[0] || "0") + 1}:00
                      </span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-muted-foreground">Precio</Label>
                    <div className="flex items-center">
                      <CreditCard className="h-4 w-4 mr-2 text-green-600" />
                      <span className="font-bold">${courtData.price.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <Label htmlFor="players">Jugadores</Label>
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div>
                      <Input id="player1" placeholder="Jugador 1 (Tú)" defaultValue="Tu Nombre" />
                    </div>
                    <div>
                      <Input id="player2" placeholder="Jugador 2" />
                    </div>
                    <div>
                      <Input id="player3" placeholder="Jugador 3" />
                    </div>
                    <div>
                      <Input id="player4" placeholder="Jugador 4" />
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="notes">Notas adicionales</Label>
                  <Input id="notes" placeholder="Notas o requerimientos especiales" className="mt-2" />
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Método de Pago</CardTitle>
                <CardDescription>Selecciona tu método de pago preferido</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="webpay">
                  <TabsList className="grid grid-cols-3 mb-4">
                    <TabsTrigger value="webpay">WebPay</TabsTrigger>
                    <TabsTrigger value="transfer">Transferencia</TabsTrigger>
                    <TabsTrigger value="onsite">Pago en Sitio</TabsTrigger>
                  </TabsList>
                  <TabsContent value="webpay">
                    <div className="space-y-4">
                      <div className="rounded-lg border p-4">
                        <RadioGroup defaultValue="credit" className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="credit" id="credit" />
                            <Label htmlFor="credit" className="flex items-center">
                              <CreditCard className="h-4 w-4 mr-2" />
                              Tarjeta de Crédito
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="debit" id="debit" />
                            <Label htmlFor="debit" className="flex items-center">
                              <CreditCard className="h-4 w-4 mr-2" />
                              Tarjeta de Débito
                            </Label>
                          </div>
                        </RadioGroup>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Serás redirigido a WebPay para completar el pago de forma segura.
                      </p>
                    </div>
                  </TabsContent>
                  <TabsContent value="transfer">
                    <div className="space-y-4">
                      <div className="rounded-lg border p-4">
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Nombre:</span>
                            <span>UCENIN SpA</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Banco:</span>
                            <span>Banco Estado</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Tipo de Cuenta:</span>
                            <span>Cuenta Corriente</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">N° de Cuenta:</span>
                            <span>12345678</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">RUT:</span>
                            <span>76.123.456-7</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Email:</span>
                            <span><EMAIL></span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="transfer">Comprobante de Transferencia</Label>
                        <Input id="transfer" type="file" className="mt-2" />
                      </div>
                    </div>
                  </TabsContent>
                  <TabsContent value="onsite">
                    <div className="space-y-4">
                      <p>
                        Podrás pagar en el complejo antes de usar la cancha. Ten en cuenta que la reserva no estará
                        confirmada hasta que se realice el pago.
                      </p>
                      <div className="rounded-lg border p-4 bg-yellow-50">
                        <p className="text-sm text-yellow-800">
                          Nota: Debes llegar al menos 15 minutos antes de tu horario reservado para realizar el pago.
                        </p>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card className="sticky top-24">
              <CardHeader>
                <CardTitle>Resumen</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Cancha:</span>
                      <span>{courtData.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Fecha:</span>
                      <span>{date.toLocaleDateString("es-ES")}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Hora:</span>
                      <span>
                        {time} - {Number.parseInt(time?.split(":")[0] || "0") + 1}:00
                      </span>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <div className="flex justify-between font-medium">
                      <span>Subtotal:</span>
                      <span>${courtData.price.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-muted-foreground">
                      <span>IVA (19%):</span>
                      <span>${Math.round(courtData.price * 0.19).toLocaleString()}</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between text-lg font-bold">
                      <span>Total:</span>
                      <span>${Math.round(courtData.price * 1.19).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full bg-green-600 hover:bg-green-700" onClick={handleConfirmReservation}>
                  Confirmar y Pagar
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
