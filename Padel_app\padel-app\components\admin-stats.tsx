"use client"

export function AdminStats() {
  return (
    <div className="w-full h-[350px] flex items-center justify-center">
      <div className="w-full h-full flex flex-col">
        <div className="text-sm font-medium mb-4">Ingresos Mensuales</div>
        <div className="flex-1 flex items-end">
          {[
            { name: "Ene", total: 890000 },
            { name: "Feb", total: 950000 },
            { name: "<PERSON>", total: 1020000 },
            { name: "Abr", total: 980000 },
            { name: "May", total: 1100000 },
            { name: "<PERSON>", total: 1248500 },
          ].map((item, index) => (
            <div key={index} className="flex-1 flex flex-col items-center">
              <div
                className="w-full max-w-[50px] bg-green-600 rounded-t-sm"
                style={{
                  height: `${(item.total / 1248500) * 280}px`,
                }}
              />
              <div className="mt-2 text-xs text-muted-foreground">{item.name}</div>
              <div className="text-xs font-medium">${(item.total / 1000).toFixed(0)}k</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
