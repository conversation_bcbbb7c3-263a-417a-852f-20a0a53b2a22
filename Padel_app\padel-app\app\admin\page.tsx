import Link from "next/link"
import { <PERSON><PERSON>hart<PERSON>, CalendarDays, CreditCard, Setting<PERSON>, Users } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { AdminReservations } from "@/components/admin-reservations"
import { AdminStats } from "@/components/admin-stats"
import { AdminCourts } from "@/components/admin-courts"

export default function AdminPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <div className="border-b">
        <div className="flex h-16 items-center px-4 container">
          <Link href="/admin" className="flex items-center gap-2 font-bold text-xl text-green-600">
            <span>UCENIN</span>
            <span className="text-sm font-normal text-muted-foreground">Admin</span>
          </Link>
          <nav className="ml-auto flex items-center space-x-4">
            <Link href="/admin/settings">
              <Button variant="ghost" size="icon">
                <Settings className="h-5 w-5" />
                <span className="sr-only">Configuración</span>
              </Button>
            </Link>
            <Link href="/">
              <Button variant="outline" size="sm">
                Salir
              </Button>
            </Link>
          </nav>
        </div>
      </div>
      <div className="grid flex-1 md:grid-cols-[220px_1fr]">
        <div className="hidden border-r bg-muted/40 md:block">
          <div className="flex h-full max-h-screen flex-col gap-2">
            <div className="flex-1 overflow-auto py-2">
              <nav className="grid items-start px-2 text-sm font-medium">
                <Link
                  href="/admin"
                  className="flex items-center gap-3 rounded-lg bg-accent px-3 py-2 text-accent-foreground"
                >
                  <BarChart3 className="h-4 w-4" />
                  Dashboard
                </Link>
                <Link
                  href="/admin/reservas"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground hover:text-foreground"
                >
                  <CalendarDays className="h-4 w-4" />
                  Reservas
                </Link>
                <Link
                  href="/admin/canchas"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground hover:text-foreground"
                >
                  <Users className="h-4 w-4" />
                  Canchas
                </Link>
                <Link
                  href="/admin/pagos"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground hover:text-foreground"
                >
                  <CreditCard className="h-4 w-4" />
                  Pagos
                </Link>
              </nav>
            </div>
          </div>
        </div>
        <div className="flex flex-col">
          <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
            <div className="flex items-center">
              <h1 className="text-lg font-semibold md:text-2xl">Dashboard</h1>
            </div>

            <Tabs defaultValue="overview" className="space-y-4">
              <TabsList>
                <TabsTrigger value="overview">Resumen</TabsTrigger>
                <TabsTrigger value="reservations">Reservas</TabsTrigger>
                <TabsTrigger value="courts">Canchas</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Ingresos Totales</CardTitle>
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">$1,248,500</div>
                      <p className="text-xs text-muted-foreground">+18.2% respecto al mes anterior</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Reservas</CardTitle>
                      <CalendarDays className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">+248</div>
                      <p className="text-xs text-muted-foreground">+12.5% respecto al mes anterior</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Tasa de Ocupación</CardTitle>
                      <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">78.2%</div>
                      <p className="text-xs text-muted-foreground">+4.3% respecto al mes anterior</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Nuevos Usuarios</CardTitle>
                      <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">+32</div>
                      <p className="text-xs text-muted-foreground">+10.1% respecto al mes anterior</p>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                  <Card className="col-span-4">
                    <CardHeader>
                      <CardTitle>Resumen de Ingresos</CardTitle>
                    </CardHeader>
                    <CardContent className="pl-2">
                      <AdminStats />
                    </CardContent>
                  </Card>
                  <Card className="col-span-3">
                    <CardHeader>
                      <CardTitle>Reservas Recientes</CardTitle>
                      <CardDescription>Has recibido 24 reservas en las últimas 24 horas</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-8">
                        <div className="flex items-center">
                          <div className="ml-4 space-y-1">
                            <p className="text-sm font-medium leading-none">Carlos Rodríguez</p>
                            <p className="text-sm text-muted-foreground">Cancha 2 • Hoy, 18:00 - 19:00</p>
                          </div>
                          <div className="ml-auto font-medium">$15,000</div>
                        </div>
                        <div className="flex items-center">
                          <div className="ml-4 space-y-1">
                            <p className="text-sm font-medium leading-none">María González</p>
                            <p className="text-sm text-muted-foreground">Cancha 1 • Hoy, 19:00 - 20:00</p>
                          </div>
                          <div className="ml-auto font-medium">$15,000</div>
                        </div>
                        <div className="flex items-center">
                          <div className="ml-4 space-y-1">
                            <p className="text-sm font-medium leading-none">Juan Pérez</p>
                            <p className="text-sm text-muted-foreground">Cancha 3 • Mañana, 10:00 - 11:00</p>
                          </div>
                          <div className="ml-auto font-medium">$20,000</div>
                        </div>
                        <div className="flex items-center">
                          <div className="ml-4 space-y-1">
                            <p className="text-sm font-medium leading-none">Ana Martínez</p>
                            <p className="text-sm text-muted-foreground">Cancha 4 • Mañana, 17:00 - 18:00</p>
                          </div>
                          <div className="ml-auto font-medium">$15,000</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="reservations" className="space-y-4">
                <AdminReservations />
              </TabsContent>

              <TabsContent value="courts" className="space-y-4">
                <AdminCourts />
              </TabsContent>
            </Tabs>
          </main>
        </div>
      </div>
    </div>
  )
}
